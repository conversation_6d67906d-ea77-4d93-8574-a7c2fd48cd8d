import tkinter as tk
from tkinter import messagebox, ttk
import ccxt
import threading
import time
import sys
import requests
from datetime import datetime, timedelta
import queue
from dataclasses import dataclass
import math

try:
    import yfinance as yf  # type: ignore
except ImportError:  # pragma: no cover - optional dependency
    yf = None

try:
    import alpaca_trade_api as tradeapi  # type: ignore
except ImportError:  # pragma: no cover - optional dependency
    tradeapi = None

try:
    import openai  # type: ignore
except ImportError:  # pragma: no cover - optional dependency
    openai = None

# Initialize exchanges - US-COMPLIANT ALTERNATIVES
# These exchanges are legal and available in the United States:
COINBASE_API_KEY = "ac775d3d-d9a8-4b5b-89b1-dd2809bd46c1"
COINBASE_SECRET = "t1rjh536B5qg2e78DWWZvJGF832cfwB7+7gNQqHRnbeLjyXZVHFkTx+Qlumxpk40gvr8BDtOKdqPrhZgpGuy6Q=="
COINBASE_PASSPHRASE = ""  # REQUIRED: Get this from your Coinbase Pro API settings
KRAKEN_API_KEY = "*********************************************************
KRAKEN_SECRET = "eimav/XSWVMVaJAtrbxyc3jW3RfXjJOa9ZYcJvW2WwD26uOub79bS8oUx3t1kpvN9R5/Lb2H351P0NVnK0S4ew=="

# US-Legal Exchange 1: Coinbase Pro (Advanced Trade)
exchange1 = ccxt.coinbase({
    'apiKey': COINBASE_API_KEY,
    'secret': COINBASE_SECRET,
    'password': COINBASE_PASSPHRASE,  # Coinbase requires passphrase
    'sandbox': False,  # Set to True for testnet
    'enableRateLimit': True,
})

# US-Legal Exchange 2: Kraken
exchange2 = ccxt.kraken({
    'apiKey': KRAKEN_API_KEY,
    'secret': KRAKEN_SECRET,
    'sandbox': False,  # Set to True for testnet
    'enableRateLimit': True,
})

# Check exchange authentication status
try:
    if exchange1.apiKey:
        balance1 = exchange1.fetch_balance()
        print(f"Coinbase Pro connected: {list(balance1['total'].keys())[:5]}")
    else:
        print("Coinbase Pro API keys not configured - read-only mode")
except Exception as e:
    print(f"Coinbase Pro connection failed: {e}")

try:
    if exchange2.apiKey:
        balance2 = exchange2.fetch_balance()
        print(f"Kraken connected: {list(balance2['total'].keys())[:5]}")
    else:
        print("Kraken API keys not configured - read-only mode")
except Exception as e:
    print(f"Kraken connection failed: {e}")

# Financial Modeling Prep API key
FMP_API_KEY = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
FMP_BASE_URL = "https://financialmodelingprep.com/api/v3/"

# Alpaca Trading API credentials
ALPACA_API_KEY = "PK2Z5BW22FCBU5KAATSD"
ALPACA_SECRET_KEY = "Ptu9GZnyGPllsdN61syzq2FaofQ5ksPcly7Sf4uB"
ALPACA_BASE_URL = "https://paper-api.alpaca.markets"  # Use paper trading for safety

# OpenAI API for deep market analysis
OPENAI_API_KEY = "********************************************************************************************************************************************************************"

# Initialize OpenAI
if openai:
    openai_client = openai.OpenAI(api_key=OPENAI_API_KEY)
    print("OpenAI connected for deep market analysis")
else:
    openai_client = None

# Initialize Alpaca API with comprehensive error handling
def initialize_alpaca():
    """Initialize Alpaca API with proper error handling and validation"""
    global alpaca

    # Validate API keys format
    if not ALPACA_API_KEY or not ALPACA_SECRET_KEY:
        print("[ERROR] Alpaca API keys are missing")
        return None

    if not ALPACA_API_KEY.startswith('PK') or len(ALPACA_API_KEY) < 20:
        print(f"[ERROR] Invalid Alpaca API key format: {ALPACA_API_KEY[:10]}...")
        return None

    if len(ALPACA_SECRET_KEY) < 20:
        print("[ERROR] Invalid Alpaca secret key format")
        return None

    try:
        print(f"[ALPACA] Connecting to: {ALPACA_BASE_URL}")
        print(f"[ALPACA] Using API Key: {ALPACA_API_KEY[:10]}...")

        # Initialize with explicit parameters
        alpaca_client = tradeapi.REST(
            key_id=ALPACA_API_KEY,
            secret_key=ALPACA_SECRET_KEY,
            base_url=ALPACA_BASE_URL,
            api_version='v2'
        )

        # Test connection
        account = alpaca_client.get_account()
        print(f"[ALPACA] ✅ Connected successfully!")
        print(f"[ALPACA] Account Status: {account.status}")
        print(f"[ALPACA] Account Type: {'Paper' if 'paper' in ALPACA_BASE_URL else 'Live'}")
        print(f"[ALPACA] Buying Power: ${float(account.buying_power):,.2f}")
        print(f"[ALPACA] Portfolio Value: ${float(account.portfolio_value):,.2f}")

        return alpaca_client

    except Exception as e:
        error_msg = str(e).lower()
        print(f"[ALPACA] ❌ Connection failed: {e}")

        if 'unauthorized' in error_msg or '401' in error_msg:
            print("[ALPACA] 🔑 Authentication Error - Possible causes:")
            print("  1. API keys are invalid or expired")
            print("  2. Keys are for live trading but using paper URL (or vice versa)")
            print("  3. Account is not properly set up")
            print("  4. Keys don't have required permissions")
            print("\n[ALPACA] 💡 Solutions:")
            print("  1. Check your Alpaca dashboard for correct paper trading keys")
            print("  2. Ensure you're using paper trading keys with paper-api.alpaca.markets")
            print("  3. Generate new API keys if needed")

        elif 'forbidden' in error_msg or '403' in error_msg:
            print("[ALPACA] 🚫 Permission Error - Your API keys don't have required permissions")

        elif 'network' in error_msg or 'connection' in error_msg:
            print("[ALPACA] 🌐 Network Error - Check your internet connection")

        else:
            print(f"[ALPACA] ❓ Unknown Error: {e}")

        return None

# Initialize Alpaca
alpaca = initialize_alpaca()

def test_alpaca_connection():
    """Test Alpaca connection and provide diagnostics"""
    if not alpaca:
        print("\n[ALPACA TEST] ❌ Alpaca client not initialized")
        return False

    try:
        # Test basic account access
        account = alpaca.get_account()
        print(f"\n[ALPACA TEST] ✅ Account access successful")
        print(f"[ALPACA TEST] Account ID: {account.id}")
        print(f"[ALPACA TEST] Status: {account.status}")

        # Test market data access
        try:
            assets = alpaca.list_assets(status='active', asset_class='us_equity')
            print(f"[ALPACA TEST] ✅ Market data access: {len(assets)} assets available")
        except Exception as e:
            print(f"[ALPACA TEST] ⚠️ Market data access limited: {e}")

        # Test order capabilities (without placing real orders)
        try:
            positions = alpaca.list_positions()
            print(f"[ALPACA TEST] ✅ Trading access: {len(positions)} positions")
        except Exception as e:
            print(f"[ALPACA TEST] ❌ Trading access failed: {e}")

        return True

    except Exception as e:
        print(f"\n[ALPACA TEST] ❌ Connection test failed: {e}")
        return False

# Test the connection
if alpaca:
    test_alpaca_connection()
else:
    print("\n[ALPACA] 🔧 To fix authentication issues:")
    print("1. Go to https://app.alpaca.markets/paper/dashboard/overview")
    print("2. Navigate to 'API Keys' section")
    print("3. Generate new Paper Trading API keys")
    print("4. Update the ALPACA_API_KEY and ALPACA_SECRET_KEY in the code")
    print("5. Ensure you're using PAPER trading keys, not LIVE trading keys")

# Global queue for trade opportunities
trade_queue = queue.Queue()
pending_trades = []
open_positions = []
positions_lock = threading.Lock()
position_monitor_thread = None
price_history = []  # Store price history for AI analysis


def safe_float_conversion(value, default=0.0):
    """Safely convert value to float with validation"""
    try:
        if value is None:
            return default
        result = float(value)
        return result if not (math.isnan(result) or math.isinf(result)) else default
    except (ValueError, TypeError):
        return default


@dataclass
class ActivePosition:
    symbol: str
    side: str
    entry_price: float
    quantity: int
    stop_loss: float
    take_profit: float
    opened_at: datetime


def start_position_monitor_thread():
    global position_monitor_thread
    if position_monitor_thread and position_monitor_thread.is_alive():
        return
    position_monitor_thread = threading.Thread(target=monitor_open_positions, daemon=True)
    position_monitor_thread.start()


def monitor_open_positions():
    while True:
        try:
            with positions_lock:
                positions_snapshot = list(open_positions)

            if not positions_snapshot:
                time.sleep(10)
                continue

            for position in positions_snapshot:
                try:
                    last_price = fetch_latest_price(position.symbol)
                except Exception as exc:
                    print(f"[ERROR] Failed to fetch price for {position.symbol}: {exc}")
                    continue

                if last_price is None:
                    continue

                should_close = False
                exit_reason = ""

                if position.side == 'buy':
                    if last_price <= position.stop_loss:
                        should_close = True
                        exit_reason = "stop_loss"
                    elif last_price >= position.take_profit:
                        should_close = True
                        exit_reason = "take_profit"
                else:
                    if last_price >= position.stop_loss:
                        should_close = True
                        exit_reason = "stop_loss"
                    elif last_price <= position.take_profit:
                        should_close = True
                        exit_reason = "take_profit"

                if should_close:
                    print(f"[EXIT] Closing {position.symbol} via {exit_reason} at ${last_price:.2f}")
                    execute_exit(position, last_price, exit_reason)
        except Exception as exc:
            print(f"[ERROR] Position monitor encountered an error: {exc}")

        time.sleep(10)


def fetch_latest_price(symbol: str):
    """Fetch latest price with comprehensive error handling"""
    if not alpaca:
        raise RuntimeError("Alpaca client unavailable for price fetch")

    try:
        bars = alpaca.get_bars(symbol, tradeapi.TimeFrame.Minute, limit=1).df
        if not bars.empty and 'close' in bars.columns:
            price = safe_float_conversion(bars['close'].iloc[-1])
            if price > 0:
                return price
    except Exception as e:
        print(f"[ERROR] Alpaca price fetch failed for {symbol}: {e}")

    # Fallback to yfinance
    try:
        if yf:
            stock = yf.Ticker(symbol)
            hist = stock.history(period="1d")
            if not hist.empty and 'Close' in hist.columns:
                price = safe_float_conversion(hist['Close'].iloc[-1])
                if price > 0:
                    return price
    except Exception as e:
        print(f"[ERROR] yfinance price fetch failed for {symbol}: {e}")

    return None


def execute_exit(position: ActivePosition, price: float, reason: str):
    if not alpaca:
        print("[ERROR] Cannot exit position because Alpaca is unavailable")
        return

    side = 'sell' if position.side == 'buy' else 'buy'

    try:
        order = alpaca.submit_order(
            symbol=position.symbol,
            qty=position.quantity,
            side=side,
            type='market',
            time_in_force='day'
        )
        print(f"[OK] Exit order submitted ({reason}): {order.id}")
    except Exception as exc:
        print(f"[ERROR] Failed to submit exit order for {position.symbol}: {exc}")
        return

    with positions_lock:
        open_positions[:] = [p for p in open_positions if not (p.symbol == position.symbol and p.side == position.side)]

def fetch_bitcoin_price(exchange):
    try:
        # Try different symbol formats for different US-legal exchanges
        if exchange.id == 'coinbase':
            ticker = exchange.fetch_ticker('BTC/USD')  # Coinbase uses USD
        elif exchange.id == 'kraken':
            ticker = exchange.fetch_ticker('BTC/USD')  # Kraken uses USD
        else:
            ticker = exchange.fetch_ticker('BTC/USD')  # Default USD format for US exchanges
        return ticker['last']
    except Exception as e:
        print(f"Failed to fetch price from {exchange.id}: {e}")  # Log to console instead of blocking GUI
        return None

def fetch_alpaca_crypto_price(symbol='BTC/USD'):
    """Fetch crypto price with fallback methods (Alpaca Paper doesn't support crypto)"""
    try:
        # Note: Alpaca Paper Trading doesn't support cryptocurrency data
        # So we'll use fallback methods immediately for paper accounts

        if alpaca and 'paper' not in ALPACA_BASE_URL:
            # Only try Alpaca crypto methods for live accounts
            try:
                alpaca_symbol = symbol.replace('/', '')  # Convert BTC/USD to BTCUSD
                bars = alpaca.get_latest_crypto_bars([alpaca_symbol])
                if bars and alpaca_symbol in bars:
                    price = safe_float_conversion(bars[alpaca_symbol].close)
                    if price > 0:
                        print(f"[ALPACA] Got crypto price via bars: ${price:.2f}")
                        return price
            except Exception as e:
                print(f"[ALPACA] Crypto bars failed: {e}")

            try:
                alpaca_symbol = symbol.replace('/', '')
                snapshot = alpaca.get_crypto_snapshot(alpaca_symbol)
                if snapshot and hasattr(snapshot, 'latest_trade') and snapshot.latest_trade:
                    price = safe_float_conversion(snapshot.latest_trade.price)
                    if price > 0:
                        print(f"[ALPACA] Got crypto price via snapshot: ${price:.2f}")
                        return price
            except Exception as e:
                print(f"[ALPACA] Crypto snapshot failed: {e}")

        # Primary method: Use FMP API for crypto prices (check if limit exceeded)
        try:
            import requests
            # Convert BTC/USD to BTCUSD format for FMP
            fmp_symbol = symbol.replace('/', '')  # BTC/USD -> BTCUSD
            url = f"https://financialmodelingprep.com/api/v4/crypto/last/{fmp_symbol}?apikey={FMP_API_KEY}"

            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, dict) and "Error Message" in data:
                    print(f"[FMP] API limit exceeded: {data['Error Message']}")
                elif data and len(data) > 0:
                    price = safe_float_conversion(data[0].get('price'))
                    if price > 0:
                        print(f"[FMP] Got {symbol} price: ${price:.2f}")
                        return price
        except Exception as e:
            print(f"[FMP] Crypto price fetch failed: {e}")

        # Fallback method: Use free CoinGecko API
        try:
            url = "https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd"
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                price = safe_float_conversion(data['bitcoin']['usd'])
                if price > 0:
                    print(f"[COINGECKO] Got BTC price: ${price:.2f}")
                    return price
        except Exception as e:
            print(f"[COINGECKO] Failed to fetch BTC price: {e}")

        # Fallback method: Use CryptoCompare API (free and reliable)
        try:
            import requests
            url = "https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD"
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                price = safe_float_conversion(data.get('USD'))
                if price > 0:
                    print(f"[CRYPTOCOMPARE] Got BTC price: ${price:.2f}")
                    return price
        except Exception as e:
            print(f"[CRYPTOCOMPARE] Failed to fetch BTC price: {e}")

        print("[ERROR] All crypto price sources failed")
        return None

    except Exception as e:
        print(f"[ERROR] Crypto price fetch error: {e}")
        return None

def detect_statistical_arbitrage(price_history, current_alpaca_price):
    """🤖 AI Statistical Arbitrage Detection Algorithm"""
    try:
        if len(price_history) < 10:
            return None

        # Extract price data for analysis
        alpaca_prices = [p['alpaca'] for p in price_history if p['alpaca']]
        coinbase_prices = [p['coinbase'] for p in price_history if p['coinbase']]
        kraken_prices = [p['kraken'] for p in price_history if p['kraken']]

        if len(alpaca_prices) < 5:
            return None

        # Calculate statistical indicators with safety checks
        if not alpaca_prices:
            return None

        alpaca_avg = sum(alpaca_prices) / len(alpaca_prices)
        alpaca_volatility = calculate_volatility(alpaca_prices)

        # Market average from other exchanges
        market_prices = []
        for i in range(min(len(coinbase_prices), len(kraken_prices))):
            if coinbase_prices[i] and kraken_prices[i] and coinbase_prices[i] > 0 and kraken_prices[i] > 0:
                market_prices.append((coinbase_prices[i] + kraken_prices[i]) / 2)

        if not market_prices:
            return None

        market_avg = sum(market_prices) / len(market_prices)

        # Statistical Arbitrage Signals (LOWERED THRESHOLDS FOR MORE OPPORTUNITIES)

        # 1. Mean Reversion Signal (Lowered threshold)
        price_deviation = current_alpaca_price - alpaca_avg
        deviation_threshold = max(10, alpaca_volatility * 0.8)  # Minimum $10 or 0.8 std dev (lowered from 1.5)

        if abs(price_deviation) > deviation_threshold:
            signal_type = "MEAN_REVERSION_SELL" if price_deviation > 0 else "MEAN_REVERSION_BUY"
            confidence = min(0.95, abs(price_deviation) / deviation_threshold * 0.8)  # Increased confidence
            expected_profit = min(abs(price_deviation) * 0.3, 100)  # FIXED: Cap at $100 max profit
            reasoning = f"Price deviated ${price_deviation:.2f} from mean (threshold: ${deviation_threshold:.2f})"
            return (signal_type, confidence, expected_profit, reasoning)

        # 2. Cross-Exchange Arbitrage Signal (Lowered threshold)
        if market_prices:
            current_market_avg = (coinbase_prices[-1] + kraken_prices[-1]) / 2 if coinbase_prices and kraken_prices else market_avg
            spread = current_alpaca_price - current_market_avg

            if abs(spread) > 5:  # $5+ spread (lowered from $25)
                signal_type = "CROSS_EXCHANGE_SELL" if spread > 0 else "CROSS_EXCHANGE_BUY"
                confidence = min(0.90, abs(spread) / 20)  # Adjusted for lower threshold
                expected_profit = min(abs(spread) * 0.8, 150)  # FIXED: Cap at $150 max profit
                reasoning = f"Alpaca vs Market spread: ${spread:.2f}"
                return (signal_type, confidence, expected_profit, reasoning)

        # 3. Momentum Signal (Lowered threshold)
        if len(alpaca_prices) >= 3:  # Reduced from 5 to 3
            recent_trend = alpaca_prices[-1] - alpaca_prices[-3]  # Shorter period
            if abs(recent_trend) > max(5, alpaca_volatility * 0.5):  # Minimum $5 movement
                signal_type = "MOMENTUM_BUY" if recent_trend > 0 else "MOMENTUM_SELL"
                confidence = min(0.85, abs(recent_trend) / (alpaca_volatility + 1))
                expected_profit = min(abs(recent_trend) * 0.5, 80)  # FIXED: Cap at $80 max profit
                reasoning = f"Strong momentum: ${recent_trend:.2f} over 3 periods"
                return (signal_type, confidence, expected_profit, reasoning)

        # 4. FORCE OPPORTUNITY FOR TESTING (if no real signals)
        if len(price_history) >= 3:
            # Create a test opportunity every 10 cycles
            if len(price_history) % 10 == 0:
                signal_type = "TEST_OPPORTUNITY_BUY"
                confidence = 0.60
                expected_profit = 25.0  # REASONABLE test profit
                reasoning = "Test opportunity generated for system validation"
                print("[🤖 DEBUG] Creating test opportunity for validation")
                return (signal_type, confidence, expected_profit, reasoning)

        return None

    except Exception as e:
        print(f"[AI ERROR] Statistical arbitrage detection failed: {e}")
        return None

def calculate_volatility(prices):
    """Calculate price volatility (standard deviation) with safety checks"""
    if len(prices) < 2:
        return 0

    # Filter out invalid prices
    valid_prices = [p for p in prices if p is not None and p > 0]
    if len(valid_prices) < 2:
        return 0

    avg = sum(valid_prices) / len(valid_prices)
    variance = sum((p - avg) ** 2 for p in valid_prices) / len(valid_prices)
    volatility = variance ** 0.5

    # Ensure non-negative result and reasonable bounds
    return max(0, min(volatility, avg * 0.5))  # Cap volatility at 50% of average price

def get_openai_market_analysis(symbol, current_price, price_history):
    """Get deep market analysis from OpenAI"""
    try:
        if not openai:
            return "OpenAI not available"

        # Prepare market data for analysis
        recent_prices = [p.get('alpaca') for p in price_history[-10:] if p.get('alpaca')]
        if len(recent_prices) < 5:
            return "Insufficient data for analysis"

        price_change = recent_prices[-1] - recent_prices[0]
        volatility = calculate_volatility(recent_prices)

        prompt = f"""
        Analyze this cryptocurrency trading opportunity:

        Symbol: {symbol}
        Current Price: ${current_price:.2f}
        Recent Price Change: ${price_change:.2f}
        Volatility: ${volatility:.2f}
        Recent Prices: {[f"${p:.2f}" for p in recent_prices]}

        Provide a brief analysis covering:
        1. Market sentiment (bullish/bearish/neutral)
        2. Key support/resistance levels
        3. Risk assessment (low/medium/high)
        4. Trading recommendation (buy/sell/hold)
        5. Confidence level (1-10)

        Keep response under 200 words.
        """

        response = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200,
            temperature=0.3
        )

        analysis = response.choices[0].message.content.strip()
        print(f"[🧠 OPENAI] Market analysis completed for {symbol}")
        return analysis

    except Exception as e:
        print(f"[🧠 OPENAI ERROR] Analysis failed: {e}")
        return f"Analysis failed: {str(e)[:100]}"

def create_ai_arbitrage_opportunity(alpaca_price, signal_type, confidence, expected_profit, reasoning):
    """Create AI-detected arbitrage opportunity with OpenAI analysis"""
    try:
        # Avoid duplicate opportunities
        for trade in pending_trades:
            if trade.strategy == 'ai_statistical_arbitrage' and signal_type in trade.reasoning:
                return

        # Get OpenAI deep analysis
        openai_analysis = get_openai_market_analysis("BTC", alpaca_price,
                                                   [p for p in globals().get('price_history', []) if p])

        # Determine trade parameters based on AI signal
        if "BUY" in signal_type:
            side = "buy"
        else:
            side = "sell"

        # Calculate position size based on confidence and expected profit
        if not alpaca:
            return

        account = alpaca.get_account()
        buying_power = float(account.buying_power)

        # FIXED: Reasonable position sizing
        risk_factor = confidence * min(1.0, expected_profit / 100)  # Higher confidence + profit = larger position
        max_position_value = min(1000, buying_power * 0.02 * risk_factor)  # Max $1000 or 2% of buying power

        btc_quantity = max_position_value / alpaca_price
        btc_quantity = max(0.001, min(btc_quantity, 0.05))  # Min 0.001 BTC, Max 0.05 BTC (~$2000)

        # Create detailed AI reasoning with OpenAI analysis
        ai_reasoning = f"🤖 AI {signal_type}: {reasoning} | Confidence: {confidence:.1%} | Expected: ${expected_profit:.2f}\n\n🧠 OpenAI Analysis: {openai_analysis[:150]}..."

        # Add AI arbitrage opportunity
        add_trade_opportunity(
            symbol="BTC-AI",
            strategy="ai_statistical_arbitrage",
            side=side,
            quantity=btc_quantity,
            current_price=alpaca_price,
            signal_strength=int(confidence * 5),  # Convert to 1-5 scale
            reasoning=ai_reasoning,
            expected_profit=expected_profit  # Pass the AI-calculated expected profit
        )

        print(f"[🤖 AI OPPORTUNITY] {signal_type} - {confidence:.1%} confidence - ${expected_profit:.2f} expected profit")
        print(f"[🧠 OPENAI] Analysis: {openai_analysis[:100]}...")

    except Exception as e:
        print(f"[AI ERROR] Failed to create AI arbitrage opportunity: {e}")

def update_prices():
    """AI-Powered Statistical Arbitrage Detection System"""
    global price_history  # Use global price history for AI analysis

    while True:
        # Get prices from multiple sources (using Alpaca as primary trading venue)
        alpaca_price = fetch_alpaca_crypto_price('BTC/USD')
        coinbase_price = fetch_bitcoin_price(exchange1)  # For price comparison only
        kraken_price = fetch_bitcoin_price(exchange2)    # For price comparison only

        # Store current prices with timestamp
        current_time = time.time()
        price_data = {
            'timestamp': current_time,
            'alpaca': alpaca_price,
            'coinbase': coinbase_price,
            'kraken': kraken_price
        }
        price_history.append(price_data)

        # Keep only last 100 price points for analysis
        if len(price_history) > 100:
            price_history.pop(0)

        print(f"[AI-PRICES] Alpaca: ${alpaca_price or 'N/A'}, Coinbase: ${coinbase_price or 'N/A'}, Kraken: ${kraken_price or 'N/A'}")

        # Update GUI - Focus on Alpaca as primary trading venue
        label_price1.config(text=f"Alpaca (Trading): ${alpaca_price:.2f}" if alpaca_price else "Alpaca: Error")
        label_price2.config(text=f"Market Avg: ${((coinbase_price or 0) + (kraken_price or 0))/2:.2f}" if coinbase_price and kraken_price else "Market: Error")

        # AI Statistical Arbitrage Detection
        if len(price_history) >= 5:  # Reduced from 10 to 5 for faster detection
            # Force create opportunities for testing if no real price data
            if not alpaca_price and (coinbase_price or kraken_price):
                # Use available price as substitute for testing
                test_price = coinbase_price or kraken_price or 43000
                print(f"[🤖 DEBUG] Using test price ${test_price:.2f} for AI analysis")
                arbitrage_signal = detect_statistical_arbitrage(price_history, test_price)

                if arbitrage_signal:
                    signal_type, confidence, expected_profit, reasoning = arbitrage_signal
                    print(f"[🤖 AI SIGNAL] {signal_type} - Confidence: {confidence:.1%} - Expected: ${expected_profit:.2f}")
                    label_differential.config(text=f"AI Signal: {signal_type} ({confidence:.1%})")
                    create_ai_arbitrage_opportunity(test_price, signal_type, confidence, expected_profit, reasoning)

            elif alpaca_price:
                arbitrage_signal = detect_statistical_arbitrage(price_history, alpaca_price)

                if arbitrage_signal:
                    signal_type, confidence, expected_profit, reasoning = arbitrage_signal
                    print(f"[🤖 AI SIGNAL] {signal_type} - Confidence: {confidence:.1%} - Expected: ${expected_profit:.2f}")
                    label_differential.config(text=f"AI Signal: {signal_type} ({confidence:.1%})")
                    create_ai_arbitrage_opportunity(alpaca_price, signal_type, confidence, expected_profit, reasoning)
                else:
                    # Show current market spread for reference
                    if coinbase_price and kraken_price:
                        market_avg = (coinbase_price + kraken_price) / 2
                        spread = abs(alpaca_price - market_avg)
                        label_differential.config(text=f"Market Spread: ${spread:.2f}")
                    else:
                        label_differential.config(text="AI Analyzing...")
            else:
                label_differential.config(text="Waiting for price data...")
        else:
            label_differential.config(text=f"AI Learning... ({len(price_history)}/5 data points)")

        time.sleep(2)  # Faster updates for statistical arbitrage (2 seconds)

def create_crypto_arbitrage_opportunity(buy_price, sell_price, differential, buy_exchange_name=None, sell_exchange_name=None):
    """Create crypto arbitrage trade opportunity for manual approval"""
    try:
        # Avoid duplicate opportunities for same price differential
        for trade in pending_trades:
            if trade.strategy == 'crypto_arbitrage' and abs(trade.current_price - abs(differential)) < 10:
                return  # Similar opportunity already exists

        # Use provided exchange names or default logic
        if not buy_exchange_name or not sell_exchange_name:
            # Legacy support - determine from price differential
            if differential > 0:  # Second exchange more expensive
                buy_exchange_name = "Coinbase"
                sell_exchange_name = "Kraken"
            else:  # First exchange more expensive
                buy_exchange_name = "Kraken"
                sell_exchange_name = "Coinbase"
                differential = abs(differential)
        
        # Calculate position size (more aggressive for arbitrage)
        if not alpaca:
            return
            
        account = alpaca.get_account()
        buying_power = float(account.buying_power)
        max_crypto_position = buying_power * 0.25  # Use 25% for crypto arbitrage (higher because it's low risk)
        btc_quantity = max_crypto_position / buy_price
        
        # Ensure minimum profitable quantity with division by zero protection
        min_profit_target = 100  # Target at least $100 profit
        if differential <= 0:
            print(f"[ERROR] Invalid differential for arbitrage: ${differential:.2f}")
            return
        min_btc_quantity = min_profit_target / max(differential, 0.01)  # Minimum differential of $0.01
        # Cap the minimum quantity to prevent excessive positions
        max_reasonable_quantity = max_crypto_position / buy_price * 2  # Max 2x the calculated position
        min_btc_quantity = min(min_btc_quantity, max_reasonable_quantity)
        btc_quantity = max(btc_quantity, min_btc_quantity)
        
        # Create detailed reasoning
        reasoning = f"Arbitrage: Buy BTC on {buy_exchange_name} at ${buy_price:,.2f}, Sell on {sell_exchange_name} at ${sell_price:,.2f}. Profit per BTC: ${differential:.2f}"
        
        # Calculate actual total profit
        total_profit = btc_quantity * differential
        
        print(f"[MONEY] Arbitrage calculation: {btc_quantity:.6f} BTC × ${differential:.2f} = ${total_profit:.2f} profit")
        
        # 🤖 AI-Enhanced Arbitrage Analysis
        opportunity = arbitrage_executor.analyze_arbitrage_opportunity(
            buy_exchange_name, sell_exchange_name, buy_price, sell_price
        )

        if opportunity:
            ai_analysis = opportunity['ai_analysis']
            print(f"[AI-ARB] 🤖 AI Analysis Complete:")
            print(f"[AI-ARB] Confidence: {opportunity['confidence']:.1%}")
            print(f"[AI-ARB] Priority: {opportunity['execution_priority']}")
            print(f"[AI-ARB] Recommended Size: {opportunity['recommended_size']:.6f} BTC")
            print(f"[AI-ARB] AI Reasoning: {ai_analysis.get('reasoning', 'N/A')}")

            # Use AI-recommended position size
            ai_btc_quantity = opportunity['recommended_size']
            ai_total_profit = ai_btc_quantity * differential

            # Auto-execute if conditions are met
            if (arbitrage_executor.auto_execute and
                ai_analysis.get('should_execute', False) and
                opportunity['confidence'] > 0.6):

                print(f"[AI-ARB] 🚀 AUTO-EXECUTING (Confidence: {opportunity['confidence']:.1%})")
                arbitrage_executor.execute_arbitrage(opportunity)
            else:
                print(f"[AI-ARB] 📋 Adding to approval queue")
                print(f"[AI-ARB] Auto-execute disabled: {not arbitrage_executor.auto_execute}")

                # Add to manual approval queue with AI enhancements
                add_trade_opportunity(
                    symbol="BTC-AI-ARB",  # AI-enhanced arbitrage
                    strategy="crypto_arbitrage",
                    side="arbitrage",
                    quantity=ai_btc_quantity,
                    current_price=differential,
                    signal_strength=int(opportunity['confidence'] * 5),
                    reasoning=f"🤖 AI Arbitrage: {ai_analysis.get('reasoning', reasoning)} | AI Profit: ${ai_total_profit:.2f}",
                    expected_profit=ai_total_profit
                )
        else:
            # Fallback to original logic if AI analysis fails
            add_trade_opportunity(
                symbol="BTC-ARB",
                strategy="crypto_arbitrage",
                side="arbitrage",
                quantity=btc_quantity,
                current_price=differential,
                signal_strength=min(5, int(differential / 20)),
                reasoning=f"{reasoning} | Total Profit: ${total_profit:.2f}"
            )
        
        print(f"[BELL] Crypto arbitrage opportunity detected: ${differential:.2f} profit per BTC")
        
    except Exception as e:
        print(f"[ERROR] Failed to create crypto arbitrage opportunity: {e}")

# Function to get earnings calendar with fallback (FMP highest tier required)
def get_earnings_calendar():
    try:
        url = f"https://financialmodelingprep.com/api/v3/earning_calendar?from={time.strftime('%Y-%m-%d')}&to={time.strftime('%Y-%m-%d')}&apikey={FMP_API_KEY}"
        print("[DATE] Attempting to fetch earnings from FMP...")
        response = requests.get(url)
        print(f"[CONN] Earnings API response status: {response.status_code}")
        
        if response.status_code == 200:
            earnings_data = response.json()
            if isinstance(earnings_data, list):
                print(f"[OK] Found {len(earnings_data)} live earnings announcements today")
                if earnings_data:
                    print(f"[UP] Sample earnings: {earnings_data[0].get('symbol', 'N/A')} at {earnings_data[0].get('time', 'N/A')}")
                return earnings_data
            else:
                print(f"[WARN] Unexpected earnings data format: {earnings_data}")
                return []
        else:
            print(f"[WARN] Earnings endpoint requires highest FMP tier (Status: {response.status_code})")
            print("[DATA] Using high-volume stocks as earnings candidates...")
            # Use market gainers as potential earnings plays (available with your subscription)
            gainers, _ = get_market_movers()
            earnings_candidates = []
            for stock in gainers[:5]:  # Top 5 gainers often have news/earnings
                earnings_candidates.append({
                    'symbol': stock.get('symbol', ''),
                    'date': time.strftime('%Y-%m-%d'),
                    'time': 'amc',
                    'reason': f"High volume gainer (+{stock.get('changesPercentage', 0):.1f}%)"
                })
            print(f"[UP] Using {len(earnings_candidates)} high-volume candidates")
            return earnings_candidates
    except Exception as e:
        print(f"[ERROR] Failed to fetch earnings calendar: {e}")
        return []

# Trade Analysis and Approval System
class TradeOpportunity:
    def __init__(self, symbol, strategy, side, quantity, current_price, signal_strength, reasoning, expected_profit=None):
        self.symbol = symbol
        self.strategy = strategy
        self.side = side
        self.quantity = quantity
        self.current_price = current_price
        self.signal_strength = signal_strength
        self.reasoning = reasoning
        self.expected_profit_override = expected_profit  # Store AI-calculated expected profit
        self.timestamp = datetime.now()
        self.projected_profit = self.calculate_projected_profit()
        self.target_date = self.calculate_target_date()
        self.risk_level = self.calculate_risk_level()
        self.stop_loss, self.take_profit = self.calculate_exit_levels()
        
    def calculate_projected_profit(self):
        """Calculate projected profit based on strategy and signal strength"""
        # Use AI-calculated expected profit if available (for AI statistical arbitrage)
        if self.expected_profit_override is not None:
            return self.expected_profit_override

        if self.strategy == 'crypto_arbitrage':
            # For crypto arbitrage, current_price is the differential (profit per unit)
            return self.quantity * self.current_price

        # Validate inputs to prevent calculation errors
        if self.quantity <= 0 or self.current_price <= 0:
            print(f"[ERROR] Invalid trade parameters: quantity={self.quantity}, price={self.current_price}")
            return 0.0

        position_value = self.quantity * self.current_price

        # Profit projections based on strategy type and signal strength
        if self.strategy == 'straddle':
            # Expect 3-8% move on high volatility
            expected_move = 0.03 + (self.signal_strength * 0.01)
            return position_value * expected_move
        elif self.strategy == 'strangle':
            # Expect 2-5% directional move
            expected_move = 0.02 + (self.signal_strength * 0.008)
            return position_value * expected_move
        elif self.strategy == 'iron_condor':
            # Conservative 1-3% gain
            expected_move = 0.01 + (self.signal_strength * 0.005)
            return position_value * expected_move
        else:
            # Default directional trade
            expected_move = 0.015 + (self.signal_strength * 0.01)
            return position_value * expected_move
        
    def calculate_target_date(self):
        """Calculate target date for profit realization"""
        if self.strategy == 'crypto_arbitrage':
            # Arbitrage opportunities should be executed immediately
            return self.timestamp + timedelta(minutes=5)
        elif self.strategy in ['straddle', 'strangle']:
            # Volatility plays - expect results within 1-3 days
            return self.timestamp + timedelta(days=2)
        elif self.strategy == 'iron_condor':
            # Conservative plays - 3-7 days
            return self.timestamp + timedelta(days=5)
        else:
            # Directional trades - 1-5 days
            return self.timestamp + timedelta(days=3)
    
    def calculate_risk_level(self):
        """Calculate risk level based on position size and volatility"""
        position_value = self.quantity * self.current_price
        if position_value > 5000:
            return "HIGH"
        elif position_value > 2000:
            return "MEDIUM"
        else:
            return "LOW"
    
    def calculate_exit_levels(self):
        """Determine stop loss and take profit targets"""
        if self.strategy == 'crypto_arbitrage':
            # For crypto arbitrage, use POSITION VALUE not BTC price for exits
            position_value = self.quantity * self.current_price

            # Set reasonable dollar-based exits
            stop_loss_amount = min(position_value * 0.05, 50)  # Max $50 loss or 5% of position
            take_profit_amount = min(position_value * 0.10, 100)  # Max $100 profit or 10% of position

            # Convert back to price levels
            if self.quantity > 0:
                stop_price = self.current_price - (stop_loss_amount / self.quantity)
                target_price = self.current_price + (take_profit_amount / self.quantity)
            else:
                stop_price = self.current_price + 50  # Fallback
                target_price = self.current_price - 50

            return max(0.01, stop_price), target_price

        elif self.strategy == 'ai_statistical_arbitrage':
            # For AI arbitrage, use POSITION VALUE not BTC price for exits
            position_value = self.quantity * self.current_price

            # Set very conservative dollar-based exits for small positions
            stop_loss_amount = min(position_value * 0.02, 20)  # Max $20 loss or 2% of position
            take_profit_amount = min(position_value * 0.05, 50)  # Max $50 profit or 5% of position

            # Convert back to price levels
            if self.quantity > 0:
                stop_price = self.current_price - (stop_loss_amount / self.quantity)
                target_price = self.current_price + (take_profit_amount / self.quantity)
            else:
                stop_price = self.current_price + 20  # Fallback
                target_price = self.current_price - 20

            return max(0.01, stop_price), target_price

        # Default for other strategies (stocks, options, etc.)
        # Use percentage-based exits for stocks
        baseline_risk = 0.02  # 2%
        risk_multiplier = baseline_risk + (self.signal_strength * 0.005)
        reward_multiplier = risk_multiplier * 2

        if self.side == 'buy':
            stop = max(0.01, self.current_price * (1 - risk_multiplier))
            target = self.current_price * (1 + reward_multiplier)
        else:
            stop = self.current_price * (1 + risk_multiplier)
            target = max(0.01, self.current_price * (1 - reward_multiplier))

        return stop, target

    def get_analysis_summary(self):
        """Return formatted analysis summary"""
        # Calculate actual position value correctly
        actual_position_value = self.quantity * self.current_price

        if self.strategy == 'crypto_arbitrage':
            return {
                'symbol': self.symbol,
                'strategy': self.strategy,
                'side': self.side.upper(),
                'quantity': f"{self.quantity:.4f} BTC",
                'current_price': f"${self.current_price:.2f} diff",
                'position_value': f"${self.projected_profit:,.2f}",
                'projected_profit': f"${self.projected_profit:,.2f}",
                'target_date': self.target_date.strftime('%H:%M'),
                'risk_level': "LOW",
                'signal_strength': f"{self.signal_strength}/5",
                'reasoning': self.reasoning,
                'stop_loss': f"${self.stop_loss:.2f}",
                'take_profit': f"${self.take_profit:.2f}"
            }
        elif self.strategy == 'ai_statistical_arbitrage':
            # Special handling for AI arbitrage - show actual BTC position value
            return {
                'symbol': self.symbol,
                'strategy': self.strategy,
                'side': self.side.upper(),
                'quantity': f"{self.quantity:.6f} BTC",
                'current_price': f"${self.current_price:.2f}",
                'position_value': f"${actual_position_value:,.2f}",
                'projected_profit': f"${self.projected_profit:,.2f}",
                'target_date': self.target_date.strftime('%H:%M'),
                'risk_level': self.risk_level,
                'signal_strength': f"{self.signal_strength}/5",
                'reasoning': self.reasoning,
                'stop_loss': f"${self.stop_loss:.2f}",
                'take_profit': f"${self.take_profit:.2f}"
            }
        else:
            return {
                'symbol': self.symbol,
                'strategy': self.strategy,
                'side': self.side.upper(),
                'quantity': f"{self.quantity:,}",
                'current_price': f"${self.current_price:.2f}",
                'position_value': f"${actual_position_value:,.2f}",
                'projected_profit': f"${self.projected_profit:,.2f}",
                'target_date': self.target_date.strftime('%Y-%m-%d'),
                'risk_level': self.risk_level,
                'signal_strength': f"{self.signal_strength}/5",
                'reasoning': self.reasoning,
                'stop_loss': f"${self.stop_loss:.2f}",
                'take_profit': f"${self.take_profit:.2f}"
            }


class ArbitrageExecutor:
    """AI-powered arbitrage execution system"""
    def __init__(self):
        self.active_arbitrages = []
        self.profit_history = []
        self.ai_enabled = True
        self.auto_execute = False  # Safety switch - set to True to enable auto-execution
        self.min_profit_threshold = 50  # Minimum $50 profit to execute
        self.max_position_size = 0.01  # Max 0.01 BTC per trade
        self.execution_mode = "SIMULATION"  # SIMULATION, IBKR, or CRYPTO

    def analyze_arbitrage_opportunity(self, buy_exchange, sell_exchange, buy_price, sell_price, symbol="BTC"):
        """AI-powered arbitrage opportunity analysis"""
        try:
            differential = sell_price - buy_price
            profit_percentage = (differential / buy_price) * 100

            # AI Analysis using OpenAI
            ai_analysis = self.get_ai_arbitrage_analysis(
                buy_exchange, sell_exchange, buy_price, sell_price, differential, profit_percentage
            )

            return {
                'buy_exchange': buy_exchange,
                'sell_exchange': sell_exchange,
                'buy_price': buy_price,
                'sell_price': sell_price,
                'differential': differential,
                'profit_percentage': profit_percentage,
                'ai_analysis': ai_analysis,
                'recommended_size': ai_analysis.get('recommended_size', 0.001),
                'confidence': ai_analysis.get('confidence', 0.5),
                'execution_priority': ai_analysis.get('priority', 'LOW')
            }
        except Exception as e:
            print(f"[AI-ARB] Analysis failed: {e}")
            return None

    def get_ai_arbitrage_analysis(self, buy_exchange, sell_exchange, buy_price, sell_price, differential, profit_pct):
        """Get AI analysis of arbitrage opportunity"""
        try:
            prompt = f"""
            BITCOIN ARBITRAGE OPPORTUNITY ANALYSIS:

            Buy Exchange: {buy_exchange} at ${buy_price:,.2f}
            Sell Exchange: {sell_exchange} at ${sell_price:,.2f}
            Price Differential: ${differential:.2f}
            Profit Percentage: {profit_pct:.3f}%

            Analyze this arbitrage opportunity and provide JSON response with:
            {{
                "risk_level": "LOW/MEDIUM/HIGH",
                "recommended_size": 0.001-0.01,
                "priority": "LOW/MEDIUM/HIGH",
                "confidence": 0.0-1.0,
                "risks": ["list of key risks"],
                "execution_time": "estimated time",
                "should_execute": true/false,
                "reasoning": "brief explanation"
            }}

            Consider: exchange reliability, liquidity, fees, market volatility, timing.
            """

            if openai_client:
                response = openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=400,
                    temperature=0.2
                )

                ai_response = response.choices[0].message.content
                print(f"[AI-ARB] AI Analysis: {ai_response[:200]}...")

                # Try to parse JSON response
                import json
                try:
                    analysis = json.loads(ai_response)
                    return analysis
                except:
                    # Extract key info if JSON parsing fails
                    return self.parse_ai_response(ai_response, differential, profit_pct)
            else:
                print("[AI-ARB] OpenAI not available, using fallback analysis")
                return self.fallback_analysis(differential, profit_pct)

        except Exception as e:
            print(f"[AI-ARB] AI analysis failed: {e}")
            return self.fallback_analysis(differential, profit_pct)

    def parse_ai_response(self, ai_response, differential, profit_pct):
        """Parse AI response when JSON parsing fails"""
        try:
            # Extract key information from text response
            confidence = 0.6
            if "high confidence" in ai_response.lower():
                confidence = 0.8
            elif "low confidence" in ai_response.lower():
                confidence = 0.4

            should_execute = "should execute" in ai_response.lower() or "recommend" in ai_response.lower()

            return {
                'risk_level': 'MEDIUM',
                'recommended_size': min(0.005, self.max_position_size),
                'priority': 'MEDIUM',
                'confidence': confidence,
                'should_execute': should_execute,
                'reasoning': ai_response[:200] + "..." if len(ai_response) > 200 else ai_response
            }
        except:
            return self.fallback_analysis(differential, profit_pct)

    def fallback_analysis(self, differential, profit_pct):
        """Fallback analysis when AI is unavailable"""
        if profit_pct > 1.0:
            priority = 'HIGH'
            confidence = 0.8
            size = min(0.01, self.max_position_size)
            should_execute = True
        elif profit_pct > 0.5:
            priority = 'MEDIUM'
            confidence = 0.6
            size = 0.005
            should_execute = True
        else:
            priority = 'LOW'
            confidence = 0.4
            size = 0.001
            should_execute = False

        return {
            'risk_level': 'MEDIUM',
            'recommended_size': size,
            'priority': priority,
            'confidence': confidence,
            'should_execute': should_execute,
            'risks': ['Price movement risk', 'Exchange execution risk'],
            'execution_time': '3-7 minutes',
            'reasoning': f'Fallback analysis: {profit_pct:.3f}% profit opportunity'
        }

    def execute_arbitrage(self, opportunity):
        """Execute arbitrage trade using selected execution mode"""
        execution_mode = self.execution_mode  # Uses GUI-selected mode

        try:
            print(f"\n[AI-ARB] 🤖 EXECUTING ARBITRAGE OPPORTUNITY ({execution_mode} MODE)")
            print(f"[AI-ARB] Buy: {opportunity['buy_exchange']} at ${opportunity['buy_price']:,.2f}")
            print(f"[AI-ARB] Sell: {opportunity['sell_exchange']} at ${opportunity['sell_price']:,.2f}")
            print(f"[AI-ARB] Profit: ${opportunity['differential']:.2f} ({opportunity['profit_percentage']:.3f}%)")
            print(f"[AI-ARB] Size: {opportunity['recommended_size']:.6f} BTC")
            print(f"[AI-ARB] AI Confidence: {opportunity['confidence']:.1%}")

            if execution_mode == "SIMULATION":
                return self.execute_simulation(opportunity)
            elif execution_mode == "IBKR":
                return self.execute_ibkr_trade(opportunity)
            elif execution_mode == "CRYPTO":
                return self.execute_crypto_arbitrage(opportunity)
            else:
                print(f"[AI-ARB] ❌ Unknown execution mode: {execution_mode}")
                return False

        except Exception as e:
            print(f"[AI-ARB] ❌ Execution failed: {e}")
            return False

    def execute_simulation(self, opportunity):
        """Simulate trade execution"""
        estimated_profit = opportunity['differential'] * opportunity['recommended_size']

        print(f"[AI-ARB] ✅ SIMULATED EXECUTION COMPLETE")
        print(f"[AI-ARB] 💰 Estimated Profit: ${estimated_profit:.2f}")

        # Add to profit history
        self.profit_history.append({
            'timestamp': datetime.now(),
            'profit': estimated_profit,
            'opportunity': opportunity,
            'execution_type': 'SIMULATION'
        })

        return True

    def execute_ibkr_trade(self, opportunity):
        """Execute trade using Interactive Brokers TWS API"""
        try:
            print(f"[IBKR] 🏦 Executing IBKR trade via TWS API...")

            # Try to import and connect to IBKR
            try:
                from ib_insync import IB, Stock, MarketOrder, LimitOrder
                print(f"[IBKR] ✅ ib_insync library loaded")
            except ImportError:
                print(f"[IBKR] ❌ ib_insync not installed. Run: pip install ib_insync")
                return self.execute_simulation(opportunity)

            # Connect to TWS
            ib = IB()
            try:
                ib.connect('127.0.0.1', 7497, clientId=1)  # Paper trading port
                print(f"[IBKR] ✅ Connected to TWS successfully")

                # Get account info
                account_summary = ib.accountSummary()
                if not account_summary:
                    print(f"[IBKR] ❌ No account data - check TWS connection")
                    ib.disconnect()
                    return self.execute_simulation(opportunity)

                print(f"[IBKR] ✅ Account verified - ready to trade")

                # For crypto arbitrage, we'd need to implement:
                # 1. Check available crypto instruments
                # 2. Place buy order on one exchange
                # 3. Place sell order on another exchange
                # 4. Monitor fills and manage risk

                print(f"[IBKR] 📋 Real IBKR crypto arbitrage implementation needed:")
                print(f"[IBKR]   - Crypto instrument mapping")
                print(f"[IBKR]   - Order placement logic")
                print(f"[IBKR]   - Fill monitoring")
                print(f"[IBKR]   - Risk management")

                # For now, simulate with real connection
                estimated_profit = opportunity['differential'] * opportunity['recommended_size']
                print(f"[IBKR] 💰 Would execute: ${estimated_profit:.2f} profit trade")
                print(f"[IBKR] 🔄 Using simulation mode until full implementation")

                ib.disconnect()

                # Add to profit history with IBKR tag
                self.profit_history.append({
                    'timestamp': datetime.now(),
                    'profit': estimated_profit,
                    'opportunity': opportunity,
                    'execution_type': 'IBKR_SIMULATION'
                })

                return True

            except Exception as e:
                print(f"[IBKR] ❌ TWS connection failed: {e}")
                print(f"[IBKR] 📋 Check:")
                print(f"[IBKR]   1. TWS/Gateway running?")
                print(f"[IBKR]   2. API enabled in TWS settings?")
                print(f"[IBKR]   3. Socket port 7497 (paper)?")
                print(f"[IBKR]   4. Trusted IP: 127.0.0.1?")

                # Fallback to simulation
                return self.execute_simulation(opportunity)

        except Exception as e:
            print(f"[IBKR] ❌ IBKR execution failed: {e}")
            return self.execute_simulation(opportunity)

    def execute_crypto_arbitrage(self, opportunity):
        """Execute real crypto arbitrage between exchanges"""
        try:
            print(f"[CRYPTO] 🪙 Executing crypto arbitrage...")
            print(f"[CRYPTO] ⚠️ Real crypto trading not implemented yet")
            print(f"[CRYPTO] 📋 To implement:")
            print(f"[CRYPTO]   1. Setup Coinbase Pro API keys")
            print(f"[CRYPTO]   2. Setup Kraken API keys")
            print(f"[CRYPTO]   3. Implement order placement")
            print(f"[CRYPTO]   4. Handle transfer between exchanges")

            # Fallback to simulation
            return self.execute_simulation(opportunity)

        except Exception as e:
            print(f"[CRYPTO] ❌ Crypto execution failed: {e}")
            return False


# Initialize the AI Arbitrage Executor
arbitrage_executor = ArbitrageExecutor()


def add_trade_opportunity(symbol, strategy, side, quantity, current_price, signal_strength, reasoning, expected_profit=None):
    """Add a new trade opportunity to the approval queue"""
    trade = TradeOpportunity(symbol, strategy, side, quantity, current_price, signal_strength, reasoning, expected_profit)
    trade_queue.put(trade)
    pending_trades.append(trade)
    print(f"[BELL] New trade opportunity added: {symbol} {strategy}")

    # Update GUI if it exists
    try:
        update_trade_opportunities_display()
    except Exception:
        pass

# Function to execute real stock trades via Alpaca (now with approval)
def execute_alpaca_stock_trade(symbol, side, quantity, order_type='market'):
    """Execute stock trades through Alpaca"""
    if not alpaca:
        print(f"[ERROR] Alpaca not connected, cannot execute {side} order for {symbol}")
        return None
    
    try:
        print(f"[DATA] Executing Alpaca {side} order: {quantity} shares of {symbol}")
        
        # Check if market is open
        clock = alpaca.get_clock()
        if not clock.is_open:
            print("[WARN] Market is closed, submitting order for next open")
        
        # Place the order
        order = alpaca.submit_order(
            symbol=symbol,
            qty=quantity,
            side=side,  # 'buy' or 'sell'
            type=order_type,  # 'market' or 'limit'
            time_in_force='day'
        )
        
        print(f"[OK] Order submitted: {order.id}")
        print(f"[UP] {side.upper()} {quantity} {symbol} at {order_type} price")
        return order
        
    except Exception as e:
        print(f"[ERROR] Failed to execute Alpaca trade for {symbol}: {e}")
        return None

# Function to place volatility trades based on market signals (now with approval system)
def place_volatility_trade(ticker, strategy, signal_strength=1, reasoning="Market signal detected"):
    """Analyze and queue volatility-based stock trades for approval"""
    try:
        print(f"[TARGET] Analyzing {strategy} trade opportunity for {ticker} (Signal: {signal_strength})")
        
        if not alpaca or not tradeapi:
            print(f"[ERROR] Alpaca not available for {ticker}")
            return
        
        # Get current stock price
        try:
            bars = alpaca.get_bars(ticker, tradeapi.TimeFrame.Day, limit=1).df
            if bars.empty:
                print(f"[ERROR] No price data available for {ticker}")
                return
            current_price = bars['close'].iloc[-1]
        except Exception as e:
            print(f"[WARN] Using yfinance for {ticker} price: {e}")
            stock = yf.Ticker(ticker)
            hist = stock.history(period="1d")
            if hist.empty:
                print(f"[ERROR] No price data available for {ticker}")
                return
            current_price = hist['Close'].iloc[-1]
        
        # Calculate position size based on account buying power
        account = alpaca.get_account()
        buying_power = float(account.buying_power)
        max_position_value = buying_power * 0.05  # Use 5% of buying power per trade
        quantity = max(1, int(max_position_value / current_price))
        
        # Determine trade parameters based on strategy
        if strategy == 'straddle':
            side = 'buy'
            reasoning = f"High volatility expected - {reasoning}"
        elif strategy == 'strangle':
            quantity = max(1, quantity // 2)  # Smaller position
            side = 'buy'
            reasoning = f"Directional move expected - {reasoning}"
        elif strategy == 'iron_condor':
            quantity = max(1, quantity // 4)  # Very small position
            side = 'buy'
            reasoning = f"Range-bound movement expected - {reasoning}"
        else:
            side = 'buy' if signal_strength > 0 else 'sell'
            reasoning = f"Signal-based {side} trade - {reasoning}"
        
        # Add to approval queue instead of executing immediately
        add_trade_opportunity(
            symbol=ticker,
            strategy=strategy,
            side=side,
            quantity=quantity,
            current_price=current_price,
            signal_strength=min(5, abs(signal_strength)),
            reasoning=reasoning
        )
            
    except Exception as e:
        print(f"[ERROR] Failed to analyze trade for {ticker}: {e}")


def register_open_position(trade: TradeOpportunity):
    start_position_monitor_thread()

    stop_loss = trade.stop_loss
    take_profit = trade.take_profit

    with positions_lock:
        existing = [p for p in open_positions if p.symbol == trade.symbol and p.side == trade.side]
        if existing:
            # Update existing position with new levels (average handling could be added here)
            position = existing[0]
            position.stop_loss = stop_loss
            position.take_profit = take_profit
            position.quantity += trade.quantity
            position.entry_price = trade.current_price
            position.opened_at = datetime.now()
            print(f"[INFO] Updated existing position for {trade.symbol}")
        else:
            open_positions.append(
                ActivePosition(
                    symbol=trade.symbol,
                    side=trade.side,
                    entry_price=trade.current_price,
                    quantity=trade.quantity,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    opened_at=datetime.now()
                )
            )
            print(f"[INFO] Registered new position for {trade.symbol}")

# Updated function to use Alpaca instead of options
def place_options_trade(ticker, strategy, days_to_expiry=7):
    """Execute Alpaca stock trades instead of options (Alpaca doesn't support options)"""
    try:
        print(f"[REFRESH] Converting {strategy} strategy to Alpaca stock trade for {ticker}")
        
        # Convert options strategies to stock equivalents
        if strategy == 'straddle':
            print(f"[UP] Straddle → Volatility stock buy for {ticker}")
            place_volatility_trade(ticker, 'straddle', signal_strength=2)
        elif strategy == 'strangle':
            print(f"[DATA] Strangle → Directional stock trade for {ticker}")
            place_volatility_trade(ticker, 'strangle', signal_strength=1)
        elif strategy == 'iron_condor':
            print(f"[TARGET] Iron Condor → Conservative stock position for {ticker}")
            place_volatility_trade(ticker, 'iron_condor', signal_strength=1)
        else:
            print(f"[DATA] Default → Stock trade for {ticker}")
            place_volatility_trade(ticker, strategy, signal_strength=1)
            
    except Exception as e:
        print(f"[ERROR] Failed to convert options trade to stock trade for {ticker}: {e}")

# Function to run the daily insider trading strategy
def run_daily_options_strategy():
    while True:
        try:
            earnings_data = get_earnings_calendar()
            print(f"Found {len(earnings_data)} earnings announcements today")
            
            for earning in earnings_data:
                # Handle both string and dict responses
                if isinstance(earning, dict):
                    ticker = earning.get('symbol', '')
                elif isinstance(earning, str):
                    ticker = earning
                else:
                    ticker = ''
                    
                if ticker:
                    print(f"Processing earnings for {ticker}")
                    place_options_trade(ticker, 'straddle')
                    place_options_trade(ticker, 'strangle')
                    place_options_trade(ticker, 'iron_condor')
                    
        except Exception as e:
            print(f"Error in daily options strategy: {e}")
            
        time.sleep(86400)  # Check daily

def start_updating():
    update_thread = threading.Thread(target=update_prices, daemon=True)
    update_thread.start()

# Function to get insider trading data with fallback
def get_insider_trading_data():
    try:
        print("[SPY] Attempting to fetch insider trading data from FMP...")
        url = f"https://financialmodelingprep.com/api/v4/insider-trading?page=0&apikey={FMP_API_KEY}"
        response = requests.get(url)
        print(f"[CONN] Insider API response status: {response.status_code}")
        
        if response.status_code == 200:
            insider_data = response.json()
            if isinstance(insider_data, list):
                print(f"[OK] Found {len(insider_data[:50])} live insider transactions")
                if insider_data:
                    print(f"[UP] Sample insider: {insider_data[0].get('symbol', 'N/A')} - {insider_data[0].get('transactionType', 'N/A')}")
                return insider_data[:50]
            elif isinstance(insider_data, dict) and 'data' in insider_data:
                data_list = insider_data['data'][:50]
                print(f"[OK] Found {len(data_list)} live insider transactions")
                return data_list
            else:
                print(f"[WARN] Unexpected insider data format: {type(insider_data)}")
                return []
        else:
            print(f"[WARN] Insider endpoint requires highest FMP tier (Status: {response.status_code})")
            print("[DATA] Using market volatility as insider sentiment proxy...")
            # Use unusual market movements as insider activity proxy
            gainers, losers = get_market_movers()
            insider_proxy = []
            
            # High gainers might indicate positive insider sentiment
            for stock in gainers[:3]:
                if stock.get('changesPercentage', 0) > 10:  # Significant moves
                    insider_proxy.append({
                        'symbol': stock.get('symbol', ''),
                        'transactionType': 'P-Purchase',
                        'securitiesTransacted': int(stock.get('changesPercentage', 0) * 1000),  # Scale by percentage
                        'filingDate': time.strftime('%Y-%m-%d'),
                        'reason': f"Unusual gain (+{stock.get('changesPercentage', 0):.1f}%)"
                    })
            
            # High losers might indicate negative insider sentiment  
            for stock in losers[:2]:
                if stock.get('changesPercentage', 0) < -10:  # Significant drops
                    insider_proxy.append({
                        'symbol': stock.get('symbol', ''),
                        'transactionType': 'S-Sale',
                        'securitiesTransacted': abs(int(stock.get('changesPercentage', 0) * 1000)),
                        'filingDate': time.strftime('%Y-%m-%d'),
                        'reason': f"Unusual drop ({stock.get('changesPercentage', 0):.1f}%)"
                    })
            
            print(f"[UP] Using {len(insider_proxy)} unusual movements as insider proxy")
            return insider_proxy
    except Exception as e:
        print(f"[ERROR] Failed to fetch insider trading data: {e}")
        return []

# Function to analyze insider trading sentiment
def analyze_insider_sentiment(insider_data, ticker):
    bullish_score = 0
    bearish_score = 0
    
    # Filter for specific ticker
    ticker_data = [data for data in insider_data if data.get('symbol', '').upper() == ticker.upper()]
    
    for data in ticker_data:
        transaction_type = data.get('transactionType', '').lower()
        shares = data.get('securitiesTransacted', 0)
        
        if 'purchase' in transaction_type or 'buy' in transaction_type:
            bullish_score += shares
        elif 'sale' in transaction_type or 'sell' in transaction_type:
            bearish_score += shares
    
    net_sentiment = bullish_score - bearish_score
    print(f"Insider sentiment for {ticker}: Bullish={bullish_score:,}, Bearish={bearish_score:,}, Net={net_sentiment:,}")
    return net_sentiment

# Function to place trades based on insider sentiment using Alpaca
def place_insider_options_trade(ticker, sentiment, days_to_expiry=7):
    try:
        print(f"[SPY] Processing insider sentiment for {ticker}: {sentiment}")
        
        # Determine trade direction and size based on sentiment strength
        if sentiment > 10000:
            print(f"[UP] Strong bullish insider sentiment for {ticker}")
            # Scale signal strength (max 5 for very strong signals)
            signal_strength = min(5, int(abs(sentiment) / 20000))
            place_volatility_trade(ticker, 'straddle', signal_strength=signal_strength)
        elif sentiment < -10000:
            print(f"[DOWN] Strong bearish insider sentiment for {ticker}")
            signal_strength = min(5, int(abs(sentiment) / 20000))
            place_volatility_trade(ticker, 'iron_condor', signal_strength=signal_strength)
        else:
            print(f"[DATA] Insider sentiment for {ticker} below trading threshold: {sentiment}")

    except Exception as e:
        print(f"[ERROR] Failed to place insider trade for {ticker}: {e}")

# Function to place directional trades based on signals
def place_signal_trade(ticker, signal_strength):
    """Place directional trades based on comprehensive analysis signals"""
    try:
        if abs(signal_strength) < 2:  # Only trade on strong signals
            print(f"[DATA] Signal strength for {ticker} below threshold: {signal_strength}")
            return
        
        side = 'buy' if signal_strength > 0 else 'sell'
        strategy_name = f"{side}_signal"
        
        print(f"[TARGET] Signal-based trade for {ticker}: {strategy_name} (strength: {signal_strength})")
        place_volatility_trade(ticker, strategy_name, signal_strength=signal_strength)
        
    except Exception as e:
        print(f"[ERROR] Failed to place signal trade for {ticker}: {e}")

# Function to run the insider-based options trading strategy
def run_insider_options_strategy():
    while True:
        try:
            print(f"\n[SPY] === Insider Trading Strategy Check at {time.strftime('%H:%M:%S')} ===")
            insider_data = get_insider_trading_data()
            print(f"[DATA] Found {len(insider_data)} recent insider transactions")
            
            if not insider_data:
                print("[WARN] No insider data available, skipping analysis")
                time.sleep(3600)
                continue
            
            # Get unique tickers from insider data
            tickers = []
            for data in insider_data:
                if isinstance(data, dict) and data.get('symbol'):
                    tickers.append(data.get('symbol'))
                elif isinstance(data, str):
                    tickers.append(data)
            
            unique_tickers = list(set(tickers))
            print(f"[TARGET] Analyzing {len(unique_tickers)} unique tickers: {unique_tickers[:5]}")
            
            for ticker in unique_tickers[:5]:  # Limit to top 5 tickers to avoid overload
                print(f"\n[UP] Analyzing insider sentiment for {ticker}")
                sentiment = analyze_insider_sentiment(insider_data, ticker)
                if abs(sentiment) > 10000:  # Only trade if there's significant sentiment
                    print(f"[ALERT] Significant insider activity detected for {ticker}")
                    place_insider_options_trade(ticker, sentiment)
                else:
                    print(f"[DATA] Minimal insider activity for {ticker} (sentiment: {sentiment})")
                    
        except Exception as e:
            print(f"[ERROR] Error in insider options strategy: {e}")
            import traceback
            traceback.print_exc()
            
        print("[SLEEP] Insider strategy sleeping for 1 hour...")
        time.sleep(3600)  # Check hourly for insider activity

def start_options_strategy():
    options_thread = threading.Thread(target=run_daily_options_strategy, daemon=True)
    options_thread.start()
    print("Daily options trading strategy started")

# Enhanced FMP API functions for comprehensive market analysis
def get_analyst_estimates(ticker):
    """Get analyst estimates and recommendations"""
    try:
        url = f"{FMP_BASE_URL}analyst-estimates/{ticker}?apikey={FMP_API_KEY}"
        response = requests.get(url)
        return response.json()[:4] if response.status_code == 200 else []
    except Exception as e:
        print(f"Failed to fetch analyst estimates for {ticker}: {e}")
        return []

def get_upgrades_downgrades(ticker):
    """Get recent analyst upgrades and downgrades"""
    try:
        url = f"{FMP_BASE_URL}upgrades-downgrades?symbol={ticker}&apikey={FMP_API_KEY}"
        response = requests.get(url)
        return response.json()[:10] if response.status_code == 200 else []
    except Exception as e:
        print(f"Failed to fetch upgrades/downgrades for {ticker}: {e}")
        return []

def get_market_news_sentiment(ticker):
    """Get news sentiment for a ticker"""
    if not yf:
        return 0, 0

    try:
        url = f"{FMP_BASE_URL}stock_news?tickers={ticker}&limit=50&apikey={FMP_API_KEY}"
        response = requests.get(url)
        news_data = response.json() if response.status_code == 200 else []
        
        # Simple sentiment analysis based on keywords
        bullish_keywords = ['beat', 'upgrade', 'strong', 'growth', 'positive', 'buy', 'outperform']
        bearish_keywords = ['miss', 'downgrade', 'weak', 'decline', 'negative', 'sell', 'underperform']
        
        sentiment_score = 0
        for article in news_data[:20]:  # Analyze recent 20 articles
            title = article.get('title', '').lower()
            text = article.get('text', '').lower()
            content = title + ' ' + text
            
            for word in bullish_keywords:
                sentiment_score += content.count(word) * 1
            for word in bearish_keywords:
                sentiment_score -= content.count(word) * 1
        
        return sentiment_score, len(news_data)
    except Exception as e:
        print(f"Failed to fetch news sentiment for {ticker}: {e}")
        return 0, 0

def get_technical_indicators(ticker):
    """Get technical indicators for a ticker"""
    if not yf:
        return 50, 0

    try:
        # Get RSI
        rsi_url = f"{FMP_BASE_URL}technical_indicator/daily/{ticker}?period=14&type=rsi&apikey={FMP_API_KEY}"
        rsi_response = requests.get(rsi_url)
        rsi_data = rsi_response.json() if rsi_response.status_code == 200 else []
        
        # Get SMA
        sma_url = f"{FMP_BASE_URL}technical_indicator/daily/{ticker}?period=20&type=sma&apikey={FMP_API_KEY}"
        sma_response = requests.get(sma_url)
        sma_data = sma_response.json() if sma_response.status_code == 200 else []
        
        current_rsi = rsi_data[0]['rsi'] if rsi_data else 50
        current_sma = sma_data[0]['sma'] if sma_data else 0
        
        return current_rsi, current_sma
    except Exception as e:
        print(f"Failed to fetch technical indicators for {ticker}: {e}")
        return 50, 0

def get_market_movers():
    """Get market gainers and losers using premium FMP API"""
    try:
        print("[DATA] Fetching live market movers from FMP Premium API...")
        gainers_url = f"{FMP_BASE_URL}stock_market/gainers?apikey={FMP_API_KEY}"
        losers_url = f"{FMP_BASE_URL}stock_market/losers?apikey={FMP_API_KEY}"
        
        print("[SEARCH] Fetching market gainers...")
        gainers_response = requests.get(gainers_url)
        print(f"[CONN] Gainers API status: {gainers_response.status_code}")
        
        print("[SEARCH] Fetching market losers...")
        losers_response = requests.get(losers_url)
        print(f"[CONN] Losers API status: {losers_response.status_code}")
        
        gainers = []
        losers = []
        
        if gainers_response.status_code == 200:
            gainers_data = gainers_response.json()
            if isinstance(gainers_data, list):
                gainers = gainers_data[:10]
                print(f"[OK] Found {len(gainers)} live gainers")
                if gainers:
                    print(f"[UP] Top gainer: {gainers[0].get('symbol', 'N/A')} (+{gainers[0].get('changesPercentage', 0):.2f}%)")
            else:
                print(f"[WARN] Unexpected gainers format: {type(gainers_data)}")
        
        if losers_response.status_code == 200:
            losers_data = losers_response.json()
            if isinstance(losers_data, list):
                losers = losers_data[:10]
                print(f"[OK] Found {len(losers)} live losers")
                if losers:
                    print(f"[DOWN] Top loser: {losers[0].get('symbol', 'N/A')} ({losers[0].get('changesPercentage', 0):.2f}%)")
            else:
                print(f"[WARN] Unexpected losers format: {type(losers_data)}")
        
        return gainers, losers
    except Exception as e:
        print(f"[ERROR] Failed to fetch market movers: {e}")
        return [], []

def comprehensive_stock_analysis(ticker):
    """Comprehensive analysis combining multiple FMP data sources"""
    try:
        print(f"\n=== Comprehensive Analysis for {ticker} ===")
        
        # Get analyst data
        estimates = get_analyst_estimates(ticker)
        upgrades = get_upgrades_downgrades(ticker)
        
        if estimates:
            latest_estimate = estimates[0]
            print(f"Analyst Estimates - EPS: ${latest_estimate.get('estimatedEpsAvg', 'N/A')}")
            print(f"Revenue Estimate: ${latest_estimate.get('estimatedRevenueAvg', 'N/A'):,}")
        
        if upgrades:
            recent_upgrade = upgrades[0]
            print(f"Recent Action: {recent_upgrade.get('gradingCompany', 'N/A')} - {recent_upgrade.get('newGrade', 'N/A')}")
        
        # Get sentiment and technical data
        sentiment, news_count = get_market_news_sentiment(ticker)
        rsi, sma = get_technical_indicators(ticker)
        
        print(f"News Sentiment Score: {sentiment} (from {news_count} articles)")
        print(f"Technical - RSI: {rsi:.2f}, SMA(20): ${sma:.2f}")
        
        # Generate trading signal
        signal_score = 0
        if sentiment > 5:
            signal_score += 1
        if sentiment < -5:
            signal_score -= 1
        if rsi < 30:  # Oversold
            signal_score += 1
        if rsi > 70:  # Overbought
            signal_score -= 1
        if upgrades and 'upgrade' in upgrades[0].get('newGrade', '').lower():
            signal_score += 1
        
        if signal_score >= 2:
            print(f"[GREEN] BULLISH SIGNAL for {ticker} (Score: {signal_score})")
        elif signal_score <= -2:
            print(f"[RED] BEARISH SIGNAL for {ticker} (Score: {signal_score})")
        else:
            print(f"[WHITE] NEUTRAL for {ticker} (Score: {signal_score})")
            
        return signal_score
        
    except Exception as e:
        print(f"Error in comprehensive analysis for {ticker}: {e}")
        return 0

def run_market_scanner():
    """Scan market for opportunities using FMP data"""
    while True:
        try:
            print(f"\n=== Market Scanner Running at {time.strftime('%H:%M:%S')} ===")

            # Get market movers
            gainers, losers = get_market_movers()

            print(f"[SCAN] Found {len(gainers)} gainers and {len(losers)} losers")
            print(f"Top Gainers: {[gainer.get('symbol', 'N/A') for gainer in gainers[:5]]}")
            print(f"Top Losers: {[loser.get('symbol', 'N/A') for loser in losers[:5]]}")

            # Analyze top movers and place trades
            for gainer in gainers[:3]:
                ticker = gainer.get('symbol', '')
                if ticker:
                    signal = comprehensive_stock_analysis(ticker)
                    if abs(signal) >= 1:  # Lowered threshold for more opportunities
                        print(f"[ALERT] Signal detected for {ticker}: {signal}")
                        place_signal_trade(ticker, signal)

            time.sleep(1800)  # Run every 30 minutes

        except Exception as e:
            print(f"Error in market scanner: {e}")
            time.sleep(1800)

def start_insider_strategy():
    insider_thread = threading.Thread(target=run_insider_options_strategy, daemon=True)
    insider_thread.start()
    print("Insider trading options strategy started")

def start_market_scanner():
    scanner_thread = threading.Thread(target=run_market_scanner, daemon=True)
    scanner_thread.start()
    print("Comprehensive market scanner started")

def test_alpaca_trade():
    """Test function to verify Alpaca trading works"""
    if not alpaca:
        print("[ERROR] Alpaca not connected")
        return
    
    try:
        print("[TEST] Testing Alpaca trade execution...")
        # Test with a small trade on a liquid stock
        place_volatility_trade('AAPL', 'test_trade', signal_strength=1)
        print("[OK] Alpaca test trade completed")
    except Exception as e:
        print(f"[ERROR] Alpaca test trade failed: {e}")

# Trade Approval Functions
def update_trade_opportunities_display():
    """Update the trade opportunities display"""
    try:
        # Clear existing items
        for item in trade_tree.get_children():
            trade_tree.delete(item)
        
        # Add pending trades
        for i, trade in enumerate(pending_trades):
            analysis = trade.get_analysis_summary()
            trade_tree.insert('', 'end', values=(
                analysis['symbol'],
                analysis['strategy'],
                analysis['side'],
                analysis['quantity'],
                analysis['current_price'],
                analysis['position_value'],
                analysis['projected_profit'],
                analysis['target_date'],
                analysis['risk_level'],
                analysis['signal_strength'],
                analysis['reasoning'][:50] + "..." if len(analysis['reasoning']) > 50 else analysis['reasoning'],
                analysis['stop_loss'],
                analysis['take_profit']
            ))
    except Exception as e:
        print(f"Error updating trade display: {e}")

def approve_selected_trade():
    """Approve and execute the selected trade"""
    try:
        selection = trade_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a trade to approve")
            return
        
        # Get selected trade index
        item = trade_tree.item(selection[0])
        symbol = item['values'][0]
        
        # Find the trade in pending list
        trade_to_execute = None
        for trade in pending_trades:
            if trade.symbol == symbol:
                trade_to_execute = trade
                break
        
        if trade_to_execute:
            if trade_to_execute.strategy == 'ai_statistical_arbitrage':
                # Handle AI statistical arbitrage through Alpaca
                execute_ai_arbitrage(trade_to_execute)
            elif trade_to_execute.strategy == 'crypto_arbitrage':
                # Handle traditional crypto arbitrage
                execute_crypto_arbitrage(trade_to_execute)
            else:
                # Execute regular stock trade
                order = execute_alpaca_stock_trade(
                    trade_to_execute.symbol,
                    trade_to_execute.side,
                    trade_to_execute.quantity
                )

                if order:
                    register_open_position(trade_to_execute)
                    # Remove from pending trades
                    pending_trades.remove(trade_to_execute)
                    update_trade_opportunities_display()
                    messagebox.showinfo("Trade Executed", f"Successfully executed {trade_to_execute.symbol} trade")
                else:
                    messagebox.showerror("Execution Failed", f"Failed to execute {trade_to_execute.symbol} trade")
        
    except Exception as e:
        messagebox.showerror("Error", f"Failed to approve trade: {e}")

# Crypto Trading Functions
def execute_crypto_buy(exchange, symbol, amount_usd):
    """Execute a crypto buy order"""
    try:
        if not exchange.apiKey:
            print(f"[ERROR] {exchange.id} API keys not configured")
            return None
            
        # Get current price to calculate quantity
        ticker = exchange.fetch_ticker(symbol)
        price = ticker['last']
        quantity = amount_usd / price
        
        print(f"[DATA] Buying {quantity:.6f} BTC on {exchange.id} at ${price:.2f}")
        
        # Place market buy order
        order = exchange.create_market_buy_order(symbol, quantity)
        print(f"[OK] Buy order executed: {order['id']}")
        return order
        
    except Exception as e:
        print(f"[ERROR] Failed to buy on {exchange.id}: {e}")
        return None

def execute_crypto_sell(exchange, symbol, quantity):
    """Execute a crypto sell order"""
    try:
        if not exchange.apiKey:
            print(f"[ERROR] {exchange.id} API keys not configured")
            return None

        ticker = exchange.fetch_ticker(symbol)
        price = ticker['last']

        print(f"[DATA] Selling {quantity:.6f} BTC on {exchange.id} at ${price:.2f}")

        # Place market sell order
        order = exchange.create_market_sell_order(symbol, quantity)
        print(f"[OK] Sell order executed: {order['id']}")
        return order

    except Exception as e:
        print(f"[ERROR] Failed to sell on {exchange.id}: {e}")
        return None

def execute_alpaca_crypto_trade(symbol, side, quantity):
    """Execute crypto trade through Alpaca"""
    try:
        if not alpaca:
            print("[ERROR] Alpaca not connected")
            return None

        # Convert symbol format (BTC/USD -> BTCUSD)
        alpaca_symbol = symbol.replace('/', '')

        print(f"[DATA] Executing Alpaca crypto {side}: {quantity:.6f} {alpaca_symbol}")

        # Place crypto order through Alpaca
        order = alpaca.submit_order(
            symbol=alpaca_symbol,
            qty=quantity,
            side=side,  # 'buy' or 'sell'
            type='market',
            time_in_force='day'
        )

        print(f"[OK] Alpaca crypto order executed: {order.id}")
        return order

    except Exception as e:
        print(f"[ERROR] Failed to execute Alpaca crypto trade: {e}")
        return None

def execute_ai_arbitrage(trade):
    """Execute AI-detected statistical arbitrage through Alpaca"""
    try:
        # Remove from pending trades first
        pending_trades.remove(trade)
        update_trade_opportunities_display()

        print(f"[🤖 AI EXECUTION] Executing {trade.side} {trade.quantity:.6f} BTC based on AI signal")

        # Execute the trade through Alpaca crypto
        order = execute_alpaca_crypto_trade('BTC/USD', trade.side, trade.quantity)

        if order:
            # Register position for monitoring
            register_open_position(trade)

            messagebox.showinfo("🤖 AI Trade Executed",
                f"AI Statistical Arbitrage Executed!\n\n"
                f"Signal: {trade.reasoning.split(':')[1].split('|')[0].strip()}\n"
                f"Side: {trade.side.upper()}\n"
                f"Quantity: {trade.quantity:.6f} BTC\n"
                f"Price: ${trade.current_price:.2f}\n"
                f"Order ID: {order.id}\n"
                f"Expected Profit: ${trade.projected_profit:.2f}")
        else:
            messagebox.showerror("🤖 AI Trade Failed",
                f"Failed to execute AI arbitrage trade for {trade.symbol}")

    except Exception as e:
        print(f"[🤖 AI ERROR] Failed to execute AI arbitrage: {e}")
        messagebox.showerror("AI Execution Error", f"AI arbitrage execution failed: {e}")

def get_exchange_balance(exchange, currency='USDT'):
    """Get exchange balance for a specific currency"""
    try:
        if not exchange.apiKey:
            return 0
        balance = exchange.fetch_balance()
        return balance[currency]['free'] if currency in balance else 0
    except Exception as e:
        print(f"[ERROR] Failed to get {exchange.id} balance: {e}")
        return 0

def execute_crypto_arbitrage(trade):
    """Execute automated crypto arbitrage trade"""
    try:
        # Remove from pending trades first
        pending_trades.remove(trade)
        update_trade_opportunities_display()

        # Parse the reasoning to determine which exchange to buy/sell
        reasoning = trade.reasoning.lower()

        if 'binance' in reasoning and 'bybit' in reasoning:
            if 'buy btc on binance' in reasoning:
                buy_exchange = exchange1  # Binance
                sell_exchange = exchange2  # Bybit
                buy_symbol = 'BTC/USDT'
                sell_symbol = 'BTC/USDT'
            else:
                buy_exchange = exchange2  # Bybit
                sell_exchange = exchange1  # Binance
                buy_symbol = 'BTC/USDT'
                sell_symbol = 'BTC/USDT'
        else:
            # Default assignment
            buy_exchange = exchange1
            sell_exchange = exchange2
            buy_symbol = 'BTC/USDT'
            sell_symbol = 'BTC/USDT'

        # Check if API keys are configured
        if not buy_exchange.apiKey or not sell_exchange.apiKey:
            messagebox.showwarning("API Keys Missing",
                "Crypto exchange API keys not configured.\n\n"
                "Add your Binance and Bybit API keys to enable automated trading.\n"
                "Currently showing manual instructions only.")

            # Show manual instructions as fallback
            show_manual_crypto_instructions(trade)
            return
        
        # Check balances
        buy_balance = get_exchange_balance(buy_exchange, 'USDT' if 'kucoin' in buy_exchange.id else 'USD')

        # Calculate required amounts
        required_usd = trade.quantity * (trade.projected_profit / trade.quantity)  # Approximate buy amount

        if buy_balance < required_usd:
            messagebox.showerror("Insufficient Balance", 
                f"Insufficient balance on {buy_exchange.id}.\n"
                f"Required: ${required_usd:.2f}\n"
                f"Available: ${buy_balance:.2f}")
            return
        
        # Execute arbitrage
        result = messagebox.askyesno("Execute Crypto Arbitrage", 
            "Execute automated crypto arbitrage?\n\n"
            f"Buy: {trade.quantity:.6f} BTC on {buy_exchange.id}\n"
            f"Sell: {trade.quantity:.6f} BTC on {sell_exchange.id}\n"
            f"Expected Profit: ${trade.projected_profit:.2f}\n\n"
            "This will execute real trades on your accounts!")
        
        if result:
            print("[ROCKET] Executing crypto arbitrage...")
            
            # Step 1: Buy BTC on cheaper exchange
            buy_order = execute_crypto_buy(buy_exchange, buy_symbol, required_usd)
            
            if buy_order:
                # Step 2: Sell BTC on expensive exchange (assuming you have BTC there)
                sell_order = execute_crypto_sell(sell_exchange, sell_symbol, trade.quantity)
                
                if sell_order:
                    messagebox.showinfo("Arbitrage Executed", 
                        "Crypto arbitrage executed successfully!\n\n"
                        f"Buy Order: {buy_order['id']}\n"
                        f"Sell Order: {sell_order['id']}\n"
                        f"Expected Profit: ${trade.projected_profit:.2f}")
                else:
                    messagebox.showerror("Partial Execution", 
                        "Buy order successful but sell order failed.\n"
                        f"You now own {trade.quantity:.6f} BTC on {buy_exchange.id}")
            else:
                messagebox.showerror("Execution Failed", "Failed to execute buy order")
        else:
            messagebox.showinfo("Arbitrage Cancelled", "Crypto arbitrage cancelled by user")
            
    except Exception as e:
        messagebox.showerror("Error", f"Failed to execute crypto arbitrage: {e}")

def show_manual_crypto_instructions(trade):
    """Show manual crypto trading instructions"""
    instructions = (
        "MANUAL CRYPTO ARBITRAGE INSTRUCTIONS:\n\n"
        f"{trade.reasoning}\n\n"
        f"STEP-BY-STEP PROCESS:\n"
        f"1. Buy {trade.quantity:.4f} BTC on the cheaper exchange\n"
        f"2. Transfer BTC to the more expensive exchange\n"
        f"3. Sell {trade.quantity:.4f} BTC on the expensive exchange\n"
        "4. Transfer USD/USDT back to original exchange\n\n"
        f"EXPECTED PROFIT: ${trade.projected_profit:,.2f}\n"
        "EXECUTION TIMEFRAME: Within 5 minutes for best results\n\n"
        "IMPORTANT NOTES:\n"
        "- Consider transfer fees and time delays\n"
        "- Monitor price movements during transfer\n"
        "- Ensure sufficient balance on both exchanges\n"
        "- Add API keys for automated execution\n\n"
        "Would you like to proceed manually?"
    )
    
    result = messagebox.askyesno("Manual Crypto Arbitrage", instructions)
    
    if result:
        messagebox.showinfo("Manual Arbitrage Approved", 
            f"Manual crypto arbitrage approved for ${trade.projected_profit:,.2f} profit.\n\n"
            "Execute the steps manually on your crypto exchanges.\n"
            "Monitor the price differential to ensure profitability.")
    else:
        messagebox.showinfo("Arbitrage Cancelled", "Crypto arbitrage opportunity cancelled.")

def reject_selected_trade():
    """Reject and remove the selected trade"""
    try:
        selection = trade_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a trade to reject")
            return
        
        # Get selected trade
        item = trade_tree.item(selection[0])
        symbol = item['values'][0]
        
        # Remove from pending trades
        for trade in pending_trades[:]:  # Use slice copy to avoid modification during iteration
            if trade.symbol == symbol:
                pending_trades.remove(trade)
                break
        
        update_trade_opportunities_display()
        messagebox.showinfo("Trade Rejected", f"Rejected {symbol} trade opportunity")
        
    except Exception as e:
        messagebox.showerror("Error", f"Failed to reject trade: {e}")

def clear_all_trades():
    """Clear all pending trades"""
    global pending_trades
    pending_trades.clear()
    update_trade_opportunities_display()
    messagebox.showinfo("🔧 FIXED", "All pending trades cleared - Profit calculations have been fixed!")

# Create the main window
root = tk.Tk()
root.title("Global Trading Platform - No Geo Restrictions")
root.geometry("1200x900")

# Crypto Section
crypto_frame = tk.Frame(root, relief=tk.RIDGE, bd=2)
crypto_frame.pack(pady=10, padx=10, fill=tk.X)

crypto_label = tk.Label(crypto_frame, text="🤖 AI Statistical Arbitrage Monitor", font=("Helvetica", 14, "bold"))
crypto_label.pack(pady=5)

label_price1 = tk.Label(crypto_frame, text="Alpaca (Trading): $0.00", font=("Helvetica", 12))
label_price1.pack(pady=5)

label_price2 = tk.Label(crypto_frame, text="Market Average: $0.00", font=("Helvetica", 12))
label_price2.pack(pady=5)

label_differential = tk.Label(crypto_frame, text="Differential: $0.00", font=("Helvetica", 12))
label_differential.pack(pady=5)

button_start_crypto = tk.Button(crypto_frame, text="🤖 Start AI Arbitrage", command=start_updating, bg="lightblue")
button_start_crypto.pack(pady=5)

def force_crypto_arbitrage():
    """Force create a crypto arbitrage opportunity for testing"""
    try:
        print("[TOOL] Force Crypto Arbitrage button clicked!")
        
        # Get current prices from labels
        price1_text = label_price1.cget("text")
        price2_text = label_price2.cget("text")
        
        print(f"[DATA] Price 1 text: {price1_text}")
        print(f"[DATA] Price 2 text: {price2_text}")
        
        # Extract prices from labels with safe parsing
        if "$" not in price1_text or "$" not in price2_text:
            print("[ERROR] Prices not available yet - start crypto monitor first")
            messagebox.showwarning("Prices Not Available", "Please start the crypto monitor first to get live prices")
            return

        try:
            price1_str = price1_text.split("$")[1].replace(",", "").strip()
            price2_str = price2_text.split("$")[1].replace(",", "").strip()
            price1 = safe_float_conversion(price1_str)
            price2 = safe_float_conversion(price2_str)

            if price1 <= 0 or price2 <= 0:
                print(f"[ERROR] Invalid prices: price1={price1}, price2={price2}")
                messagebox.showerror("Invalid Prices", "Invalid price data detected")
                return

            differential = price2 - price1
        except (IndexError, ValueError) as e:
            print(f"[ERROR] Failed to parse prices: {e}")
            messagebox.showerror("Parse Error", "Failed to parse price data from labels")
            return
        
        print(f"[UP] KuCoin: ${price1:.2f}")
        print(f"[UP] Kraken: ${price2:.2f}")
        print(f"[MONEY] Differential: ${differential:.2f}")
        
        if abs(differential) < 10:
            messagebox.showinfo("Low Differential", f"Current differential (${differential:.2f}) is too low for profitable arbitrage")
            return
        
        print(f"[TOOL] Creating crypto arbitrage opportunity with differential: ${differential:.2f}")
        create_crypto_arbitrage_opportunity(price1, price2, differential)
        
        print("[OK] Arbitrage opportunity created successfully")
        messagebox.showinfo("Opportunity Created", f"Crypto arbitrage opportunity created with ${differential:.2f} differential")
        
    except Exception as e:
        print(f"[ERROR] Failed to force crypto arbitrage: {e}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("Error", f"Failed to create crypto opportunity: {e}")

button_force_crypto = tk.Button(crypto_frame, text="[TOOL] Force Crypto Arbitrage", command=force_crypto_arbitrage, bg="orange")
button_force_crypto.pack(pady=5)

# AI Arbitrage Controls
ai_frame = tk.Frame(crypto_frame, relief=tk.RIDGE, bd=1)
ai_frame.pack(pady=5, padx=5, fill=tk.X)

ai_label = tk.Label(ai_frame, text="🤖 AI Arbitrage Controls", font=("Helvetica", 10, "bold"))
ai_label.pack(pady=2)

def toggle_auto_execute():
    """Toggle AI auto-execution"""
    arbitrage_executor.auto_execute = not arbitrage_executor.auto_execute
    status = "ENABLED" if arbitrage_executor.auto_execute else "DISABLED"
    color = "green" if arbitrage_executor.auto_execute else "red"
    button_auto_execute.config(text=f"Auto-Execute: {status}", bg=color)
    print(f"[AI-ARB] Auto-execution {status}")

button_auto_execute = tk.Button(ai_frame, text="Auto-Execute: DISABLED", command=toggle_auto_execute, bg="red", fg="white")
button_auto_execute.pack(pady=2)

def show_ai_profit_history():
    """Show AI arbitrage profit history"""
    if not arbitrage_executor.profit_history:
        messagebox.showinfo("Profit History", "No AI arbitrage trades executed yet.")
        return

    total_profit = sum(trade['profit'] for trade in arbitrage_executor.profit_history)
    trade_count = len(arbitrage_executor.profit_history)
    avg_profit = total_profit / trade_count if trade_count > 0 else 0

    history_text = f"AI Arbitrage Performance:\n\n"
    history_text += f"Total Trades: {trade_count}\n"
    history_text += f"Total Profit: ${total_profit:.2f}\n"
    history_text += f"Average Profit: ${avg_profit:.2f}\n\n"
    history_text += "Recent Trades:\n"

    for trade in arbitrage_executor.profit_history[-5:]:  # Show last 5 trades
        timestamp = trade['timestamp'].strftime('%H:%M:%S')
        profit = trade['profit']
        history_text += f"{timestamp}: ${profit:.2f}\n"

    messagebox.showinfo("AI Profit History", history_text)

button_profit_history = tk.Button(ai_frame, text="View AI Profit History", command=show_ai_profit_history, bg="blue", fg="white")
button_profit_history.pack(pady=2)

# Trading Platform Selection
platform_frame = tk.Frame(ai_frame, relief=tk.RIDGE, bd=1)
platform_frame.pack(pady=2, fill=tk.X)

platform_label = tk.Label(platform_frame, text="Execution Platform:", font=("Helvetica", 9))
platform_label.pack(side=tk.LEFT, padx=2)

def change_execution_mode():
    """Change the execution mode for arbitrage"""
    selected = execution_mode_var.get()
    arbitrage_executor.execution_mode = selected

    if selected == "SIMULATION":
        status_text = "SIMULATION (Safe)"
        color = "gray"
    elif selected == "IBKR":
        status_text = "IBKR (Setup Required)"
        color = "orange"
    elif selected == "CRYPTO":
        status_text = "CRYPTO (Setup Required)"
        color = "purple"

    execution_status_label.config(text=status_text, fg=color)
    print(f"[PLATFORM] Execution mode changed to: {selected}")

execution_mode_var = tk.StringVar(value="SIMULATION")
execution_dropdown = tk.OptionMenu(platform_frame, execution_mode_var, "SIMULATION", "IBKR", "CRYPTO", command=lambda x: change_execution_mode())
execution_dropdown.pack(side=tk.LEFT, padx=2)

execution_status_label = tk.Label(platform_frame, text="SIMULATION (Safe)", fg="gray", font=("Helvetica", 8))
execution_status_label.pack(side=tk.LEFT, padx=5)

def test_ibkr_connection():
    """Test IBKR connection"""
    try:
        print("[IBKR] Testing IBKR connection...")

        # Try to import ib_insync
        try:
            from ib_insync import IB
            print("[IBKR] ✅ ib_insync library found")
        except ImportError:
            print("[IBKR] ❌ ib_insync not installed")
            messagebox.showerror("Missing Library", "Please install: pip install ib_insync")
            return

        # Try to connect
        ib = IB()
        try:
            ib.connect('127.0.0.1', 7497, clientId=1)  # Paper trading port
            print("[IBKR] ✅ Connected to IBKR successfully!")

            # Get account info
            account_summary = ib.accountSummary()
            if account_summary:
                print(f"[IBKR] ✅ Account access confirmed")
                messagebox.showinfo("IBKR Connection", "✅ IBKR Connected Successfully!\n\nReady for real trading!")
            else:
                print("[IBKR] ⚠️ Connected but no account data")
                messagebox.showwarning("IBKR Connection", "Connected but no account data.\nCheck TWS/Gateway settings.")

            ib.disconnect()

        except Exception as e:
            print(f"[IBKR] ❌ Connection failed: {e}")
            error_msg = f"""❌ IBKR Connection Failed

Error: {str(e)}

Setup Checklist:
1. ✅ IBKR account created?
2. ✅ TWS/Gateway running?
3. ✅ API enabled in TWS settings?
4. ✅ Socket port 7497 (paper)?
5. ✅ Trusted IP: 127.0.0.1?

Need help? Check TRADING_PLATFORM_SETUP.md"""
            messagebox.showerror("IBKR Connection Failed", error_msg)

    except Exception as e:
        print(f"[IBKR] ❌ Test failed: {e}")
        messagebox.showerror("Test Failed", f"IBKR test failed: {e}")

def show_platform_setup():
    """Show platform setup instructions"""
    setup_text = """
🏆 TRADING PLATFORM SETUP

Current Status: SIMULATION MODE (Safe)

🔑 IBKR API ACCESS (No API Key Needed!)

IBKR uses socket connection instead of API keys:

Setup Steps:
1. Create IBKR paper account (FREE)
2. Download TWS/Gateway software
3. Install: pip install ib_insync
4. Enable API in TWS settings
5. Test connection below
6. Change mode to "IBKR"

🪙 CRYPTO OPTION: Direct Exchange APIs
✅ Coinbase Advanced + Kraken
✅ Real crypto arbitrage

See TRADING_PLATFORM_SETUP.md for details!
    """
    messagebox.showinfo("Platform Setup Guide", setup_text)

button_test_ibkr = tk.Button(platform_frame, text="Test IBKR", command=test_ibkr_connection, bg="yellow")
button_test_ibkr.pack(side=tk.RIGHT, padx=2)

button_setup_guide = tk.Button(platform_frame, text="Setup Guide", command=show_platform_setup, bg="lightblue")
button_setup_guide.pack(side=tk.RIGHT, padx=2)

# Options Section
options_frame = tk.Frame(root, relief=tk.RIDGE, bd=2)
options_frame.pack(pady=10, padx=10, fill=tk.X)

options_label = tk.Label(options_frame, text="Options Earnings Strategy", font=("Helvetica", 14, "bold"))
options_label.pack(pady=5)

options_info = tk.Label(options_frame, text="Earnings, insider sentiment & comprehensive market analysis", font=("Helvetica", 10))
options_info.pack(pady=5)

button_start_options = tk.Button(options_frame, text="Start Options Strategy", command=start_options_strategy, bg="lightgreen")
button_start_options.pack(pady=5)

button_start_insider = tk.Button(options_frame, text="Start Insider Strategy", command=start_insider_strategy, bg="orange")
button_start_insider.pack(pady=5)

button_start_scanner = tk.Button(options_frame, text="Start Market Scanner", command=start_market_scanner, bg="purple", fg="white")
button_start_scanner.pack(pady=5)

# Alpaca Account Section
alpaca_frame = tk.Frame(root, relief=tk.RIDGE, bd=2)
alpaca_frame.pack(pady=10, padx=10, fill=tk.X)

alpaca_label = tk.Label(alpaca_frame, text="Alpaca Trading Account", font=("Helvetica", 14, "bold"))
alpaca_label.pack(pady=5)

if alpaca:
    try:
        account = alpaca.get_account()
        account_status = tk.Label(alpaca_frame, text=f"Status: {account.status} | Buying Power: ${float(account.buying_power):,.2f}", font=("Helvetica", 10))
        account_status.pack(pady=5)
        
        portfolio_value = tk.Label(alpaca_frame, text=f"Portfolio Value: ${float(account.portfolio_value):,.2f}", font=("Helvetica", 10))
        portfolio_value.pack(pady=5)
    except Exception:
        account_status = tk.Label(alpaca_frame, text="Account info unavailable", font=("Helvetica", 10))
        account_status.pack(pady=5)
else:
    account_status = tk.Label(alpaca_frame, text="Alpaca not connected", font=("Helvetica", 10), fg="red")
    account_status.pack(pady=5)

# Add test trade button
if alpaca:
    test_button = tk.Button(alpaca_frame, text="Test Alpaca Trade", command=test_alpaca_trade, bg="yellow")
    test_button.pack(pady=5)

# Trade Opportunities Section
trades_frame = tk.Frame(root, relief=tk.RIDGE, bd=2)
trades_frame.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)

trades_label = tk.Label(trades_frame, text="Trade Opportunities - Manual Approval Required", font=("Helvetica", 14, "bold"))
trades_label.pack(pady=5)

# Create Treeview for trade opportunities
columns = ('Symbol', 'Strategy', 'Side', 'Quantity', 'Price', 'Position Value', 'Projected Profit', 'Target Date', 'Risk', 'Signal', 'Reasoning', 'Stop Loss', 'Take Profit')
trade_tree = ttk.Treeview(trades_frame, columns=columns, show='headings', height=8)

# Define column headings and widths
column_widths = [60, 80, 50, 70, 70, 100, 100, 80, 60, 60, 160, 80, 80]
for i, col in enumerate(columns):
    trade_tree.heading(col, text=col)
    trade_tree.column(col, width=column_widths[i])

trade_tree.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)

# Add scrollbar to treeview
scrollbar = ttk.Scrollbar(trades_frame, orient=tk.VERTICAL, command=trade_tree.yview)
trade_tree.configure(yscrollcommand=scrollbar.set)
scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

# Trade control buttons
button_frame = tk.Frame(trades_frame)
button_frame.pack(pady=10)

approve_btn = tk.Button(button_frame, text="[OK] Approve Trade", command=approve_selected_trade, bg="lightgreen", font=("Helvetica", 12))
approve_btn.pack(side=tk.LEFT, padx=10)

reject_btn = tk.Button(button_frame, text="[ERROR] Reject Trade", command=reject_selected_trade, bg="lightcoral", font=("Helvetica", 12))
reject_btn.pack(side=tk.LEFT, padx=10)

clear_btn = tk.Button(button_frame, text="[TRASH] Clear All", command=clear_all_trades, bg="lightgray", font=("Helvetica", 12))
clear_btn.pack(side=tk.LEFT, padx=10)

refresh_btn = tk.Button(button_frame, text="[REFRESH] Refresh", command=update_trade_opportunities_display, bg="lightblue", font=("Helvetica", 12))
refresh_btn.pack(side=tk.LEFT, padx=10)

# Status Section
status_frame = tk.Frame(root, relief=tk.RIDGE, bd=2)
status_frame.pack(pady=10, padx=10, fill=tk.X)

status_label = tk.Label(status_frame, text="System Status", font=("Helvetica", 14, "bold"))
status_label.pack(pady=5)

status_text = tk.Text(status_frame, height=6, width=60)
status_text.pack(pady=5)

# Redirect print statements to status window
class StatusRedirect:
    def __init__(self, text_widget):
        self.text_widget = text_widget

    def write(self, string):
        self.text_widget.insert(tk.END, string)
        self.text_widget.see(tk.END)
        
    def flush(self):
        pass

sys.stdout = StatusRedirect(status_text)

# Auto-start all opportunity detection strategies
print("[STARTUP] Auto-starting all opportunity detection strategies...")
start_updating()  # Start crypto arbitrage monitoring
start_market_scanner()  # Start market scanner
start_insider_strategy()  # Start insider trading strategy
start_options_strategy()  # Start options/earnings strategy
print("[STARTUP] All strategies started successfully!")

# Run the application
root.mainloop()